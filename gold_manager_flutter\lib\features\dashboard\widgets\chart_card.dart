import 'package:flutter/material.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/theme/app_theme.dart';

/// 仪表盘图表卡片容器
/// 为图表提供统一的容器样式和布局
class DashboardChartCard extends StatelessWidget {
  /// 图表标题
  final String title;
  
  /// 图表子标题(可选)
  final String? subtitle;
  
  /// 图表图标
  final IconData? icon;
  
  /// 图表内容
  final Widget chart;
  
  /// 卡片高度
  final double? height;
  
  /// 卡片宽度
  final double? width;
  
  /// 是否显示更多按钮
  final bool showMoreButton;
  
  /// 更多按钮点击回调
  final VoidCallback? onMorePressed;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 加载提示文本
  final String loadingText;
  
  /// 自定义头部操作组件
  final Widget? headerAction;

  const DashboardChartCard({
    super.key,
    required this.title,
    required this.chart,
    this.subtitle,
    this.icon,
    this.height,
    this.width,
    this.showMoreButton = false,
    this.onMorePressed,
    this.isLoading = false,
    this.loadingText = '加载中...',
    this.headerAction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 图表标题栏
          _buildHeader(),

          // 图表内容区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: isLoading ? _buildLoadingWidget() : chart,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppBorderStyles.borderColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧标题区域
          Expanded(
            child: Row(
              children: [
                // 图标
                if (icon != null) ...[
                  Icon(
                    icon,
                    size: 20,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 8),
                ],
                
                // 标题和子标题
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          subtitle!,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // 右侧操作区域
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 自定义头部操作
              if (headerAction != null) ...[
                headerAction!,
                const SizedBox(width: 8),
              ],
              
              // 更多按钮
              if (showMoreButton)
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: onMorePressed,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.more_vert,
                        size: 18,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建加载组件
  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(
            width: 32,
            height: 32,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            loadingText,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

/// 简化版图表卡片
/// 用于较小的图表或统计组件
class SimpleChartCard extends StatelessWidget {
  final String title;
  final Widget chart;
  final double? height;
  final double? width;
  final EdgeInsetsGeometry? padding;
  final bool isLoading;

  const SimpleChartCard({
    super.key,
    required this.title,
    required this.chart,
    this.height,
    this.width,
    this.padding,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      padding: padding ?? const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 简单标题
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // 图表内容
          Expanded(
            child: isLoading
                ? const Center(
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  )
                : chart,
          ),
        ],
      ),
    );
  }
}



/// 图表卡片网格布局
/// 用于响应式布局多个图表卡片
class ChartCardGrid extends StatelessWidget {
  final List<Widget> children;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;

  const ChartCardGrid({
    super.key,
    required this.children,
    this.crossAxisCount = 2,
    this.mainAxisSpacing = 16,
    this.crossAxisSpacing = 16,
    this.childAspectRatio = 1.5,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(16),
      child: GridView.count(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        childAspectRatio: childAspectRatio,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: children,
      ),
    );
  }
}
