/// 性能优化配置
/// 针对大数据量场景的性能参数配置
class PerformanceConfig {
  // 缓存配置
  static const Duration coreDataCacheValidDuration = Duration(minutes: 10);
  static const Duration businessDataCacheValidDuration = Duration(minutes: 15);
  static const Duration autoRefreshInterval = Duration(minutes: 3);
  
  // 数据加载配置
  static const int maxConcurrentApiCalls = 2; // 最大并发API调用数
  static const Duration batchLoadDelay = Duration(milliseconds: 200); // 批次加载间隔
  static const Duration quickRestoreDelay = Duration(milliseconds: 300); // 快速恢复延迟
  
  // UI渲染配置
  static const int maxItemsPerPage = 50; // 每页最大项目数
  static const Duration uiUpdateThrottle = Duration(milliseconds: 100); // UI更新节流
  static const Duration skeletonMinShowTime = Duration(milliseconds: 500); // 骨架屏最小显示时间
  
  // 性能阈值配置
  static const int slowOperationThreshold = 2000; // 慢操作阈值（毫秒）
  static const int verySlowOperationThreshold = 5000; // 极慢操作阈值（毫秒）
  static const int maxMemoryUsageMB = 512; // 最大内存使用量（MB）
  
  // 大数据量优化配置
  static const int largeDatasetThreshold = 1000; // 大数据集阈值
  static const int hugeDatasetThreshold = 5000; // 巨大数据集阈值
  static const bool enableVirtualScrolling = true; // 启用虚拟滚动
  static const bool enableLazyLoading = true; // 启用懒加载
  
  // 网络优化配置
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // 调试配置
  static const bool enablePerformanceMonitoring = true;
  static const bool enableDetailedLogging = true;
  static const bool showPerformanceOverlay = false; // 生产环境应为false
}

/// 性能优化策略
class PerformanceStrategy {
  /// 根据数据量选择加载策略
  static LoadingStrategy getLoadingStrategy(int itemCount) {
    if (itemCount > PerformanceConfig.hugeDatasetThreshold) {
      return LoadingStrategy.virtualScrolling;
    } else if (itemCount > PerformanceConfig.largeDatasetThreshold) {
      return LoadingStrategy.lazyLoading;
    } else {
      return LoadingStrategy.standard;
    }
  }
  
  /// 根据设备性能选择渲染策略
  static RenderingStrategy getRenderingStrategy() {
    // 这里可以根据设备性能指标来决定
    // 暂时返回标准策略
    return RenderingStrategy.standard;
  }
  
  /// 获取缓存策略
  static CacheStrategy getCacheStrategy(String dataType) {
    switch (dataType) {
      case 'core':
        return CacheStrategy.aggressive; // 核心数据使用激进缓存
      case 'business':
        return CacheStrategy.moderate; // 业务数据使用适中缓存
      case 'detail':
        return CacheStrategy.conservative; // 详细数据使用保守缓存
      default:
        return CacheStrategy.moderate;
    }
  }
}

/// 加载策略枚举
enum LoadingStrategy {
  standard,      // 标准加载
  lazyLoading,   // 懒加载
  virtualScrolling, // 虚拟滚动
}

/// 渲染策略枚举
enum RenderingStrategy {
  standard,      // 标准渲染
  optimized,     // 优化渲染
  minimal,       // 最小渲染
}

/// 缓存策略枚举
enum CacheStrategy {
  conservative,  // 保守缓存（短时间）
  moderate,      // 适中缓存（中等时间）
  aggressive,    // 激进缓存（长时间）
}

/// 性能指标
class PerformanceMetrics {
  static int _totalApiCalls = 0;
  static int _cacheHits = 0;
  static int _cacheMisses = 0;
  static final List<int> _loadTimes = [];
  
  static void recordApiCall() => _totalApiCalls++;
  static void recordCacheHit() => _cacheHits++;
  static void recordCacheMiss() => _cacheMisses++;
  static void recordLoadTime(int milliseconds) => _loadTimes.add(milliseconds);
  
  static double get cacheHitRate => 
    (_cacheHits + _cacheMisses) > 0 ? _cacheHits / (_cacheHits + _cacheMisses) : 0.0;
    
  static double get averageLoadTime => 
    _loadTimes.isNotEmpty ? _loadTimes.reduce((a, b) => a + b) / _loadTimes.length : 0.0;
    
  static Map<String, dynamic> getMetrics() {
    return {
      'totalApiCalls': _totalApiCalls,
      'cacheHitRate': (cacheHitRate * 100).toStringAsFixed(1) + '%',
      'averageLoadTime': '${averageLoadTime.toStringAsFixed(1)}ms',
      'totalLoadOperations': _loadTimes.length,
    };
  }
  
  static void reset() {
    _totalApiCalls = 0;
    _cacheHits = 0;
    _cacheMisses = 0;
    _loadTimes.clear();
  }
}
