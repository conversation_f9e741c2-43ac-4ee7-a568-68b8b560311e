# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
请你总是用中文和我交流。
## Project Overview

This is a Gold Manager System (黄金管理系统) with a FastAPI backend and Flutter frontend for jewelry store management. The system is designed to be compatible with existing FastAdmin database structure while providing modern API and mobile/desktop applications.

## Development Commands

### Backend (FastAPI)

```bash
# Navigate to backend directory
cd GoldManager_FastAdmin_API

# Create virtual environment (first time only)
python -m venv jewelry_env  # Windows: jewelry_env_mac on macOS

# Activate virtual environment
# Windows:
jewelry_env\Scripts\activate
# macOS/Linux:
source jewelry_env_mac/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp env_config_example.txt env_config.txt
# Edit env_config.txt with database credentials

# Run development server
python main.py
# Server runs on http://localhost:8000
# API docs: http://localhost:8000/docs

# Run tests
pytest

# Code quality checks (using Ruff)
make lint          # Run linting
make format        # Format code
make check         # Full quality check
ruff check app/    # Direct linting
ruff format app/   # Direct formatting
```

### Frontend (Flutter)

```bash
# Navigate to frontend directory
cd gold_manager_flutter

# Install dependencies
flutter pub get

# Run development
flutter run -d windows    # Windows desktop
flutter run -d chrome     # Web browser
flutter run              # Connected device

# Quick start (Windows)
# Use batch script for convenience
..\start_flutter.bat     # Automated Windows build and run

# Build release
flutter build windows --release
flutter build apk --release
flutter build ios --release
flutter build web --release

# Run tests
flutter test

# Analyze code
flutter analyze

# Format code
dart format .
```

## Development Tools and Automation

### Backend Automation (Makefile)

The backend provides a comprehensive Makefile for common development tasks:

```bash
make help        # Show all available commands
make install     # Install project dependencies
make dev         # Install development dependencies
make run         # Start development server
make test        # Run tests
make lint        # Run code linting
make format      # Format code
make check       # Full code quality check (uses scripts/code_quality.sh)
make clean       # Clean temporary files
make requirements # Update dependencies list
make ci          # CI/CD check pipeline
```

### Environment Setup Scripts

The project includes automated setup scripts in `GoldManager_FastAdmin_API/scripts/`:

- `install_dependencies.sh`: Complete environment setup including virtual environment creation, dependency installation, and verification
- `code_quality.sh`: Comprehensive code quality checks with reporting

### Windows Development Support

- `start_flutter.bat`: Automated Flutter build and run for Windows desktop
- All scripts are designed to work in Windows environments with appropriate adaptations

## Architecture Overview

### Backend Architecture (FastAPI)

The backend follows a layered architecture pattern with clear separation of concerns:

1. **API Layer** (`app/api/api_v1/endpoints/`): RESTful endpoints organized by feature
   - Each endpoint file handles HTTP requests/responses for a specific domain
   - Uses FastAPI's dependency injection for auth, database sessions, and permissions
   
2. **Service Layer** (`app/services/`): Business logic encapsulation
   - Services contain the core business rules and orchestrate database operations
   - Example: `JewelryService` handles cost calculations, inventory checks
   
3. **Model Layer** (`app/models/`): SQLAlchemy ORM models
   - Maps to existing FastAdmin database tables (all prefixed with `fa_`)
   - Defines relationships between entities using SQLAlchemy's relationship system
   
4. **Schema Layer** (`app/schemas/`): Pydantic models for validation
   - Request/response data validation and serialization
   - Separate schemas for create, update, and response operations

5. **Core Layer** (`app/core/`): Framework configuration and utilities
   - Database configuration and session management
   - JWT authentication implementation
   - Security middleware and CORS settings

### Frontend Architecture (Flutter + GetX)

The frontend uses GetX for state management with a feature-based module structure:

1. **GetX Pattern**: Every feature follows the MVC pattern
   - **Controller**: Business logic and state management (extends GetxController)
   - **View**: UI components (typically ends with View or Page)
   - **Binding**: Dependency injection for the feature
   - **Service**: API calls and data persistence
   - **Model**: Data structures matching API responses

2. **Service Initialization Order** (in `main.dart`):
   ```dart
   StorageService → ApiService → ApiClient → AuthService → Feature Services
   ```

3. **State Management**: 
   - Use `.obs` for reactive variables
   - Use `Obx()` widget to observe state changes
   - Controllers manage business logic, views are purely presentational

4. **API Integration**:
   - All API calls go through `ApiService` or legacy `ApiProvider`
   - Automatic token injection via interceptors
   - Centralized error handling and loading states

## Key Architectural Patterns

### Authentication Flow
1. **Backend**: FastAdmin-compatible password verification (salt + md5 hash)
2. **JWT Generation**: Includes user permissions in token payload
3. **Frontend Storage**: Tokens stored in secure storage
4. **Automatic Injection**: Auth interceptor adds token to all requests
5. **Permission Checks**: Both UI and API enforce role-based access

### Database Access Pattern
- **Session per Request**: Each API request gets a fresh database session
- **Relationship Loading**: Use `joinedload` for eager loading to avoid N+1 queries
- **Transaction Management**: Service methods handle transaction boundaries
- **Query Optimization**: Complex queries use SQLAlchemy's query builder

### Cross-Cutting Concerns

1. **Error Handling**:
   - Backend: Global exception handlers return consistent error responses
   - Frontend: Try-catch blocks with user-friendly error messages

2. **Validation**:
   - Backend: Pydantic models validate all input data
   - Frontend: Form validation before API calls

3. **Logging**:
   - Backend: Loguru for structured logging to `logs/api.log`
   - Frontend: Custom logger service for debugging

4. **Security**:
   - HTTPS support with SSL certificates
   - Rate limiting on API endpoints
   - Input sanitization and SQL injection prevention
   - CORS configuration for web clients

## Common Development Tasks

### Adding a New Feature

1. **Backend**:
   - Create model in `app/models/`
   - Create schemas in `app/schemas/`
   - Create service in `app/services/`
   - Create endpoint in `app/api/api_v1/endpoints/`
   - Add route to `app/api/api_v1/api.py`

2. **Frontend**:
   - Create feature folder in `lib/features/`
   - Create model, controller, view, service, and binding
   - Add route to `lib/core/routes/app_routes.dart`
   - Register binding in `lib/core/routes/app_pages.dart`

### Database Modifications
- Models follow FastAdmin naming convention (`fa_` prefix)
- Use integer timestamps for compatibility
- Maintain backward compatibility with existing data

### UI Development Guidelines
- Use `AppBorderStyles` for consistent borders
- Follow Material Design principles
- Implement responsive layouts for mobile/desktop
- Use `CustomCard`, `CustomTextField` for consistency

## Important Files and Patterns

### Configuration Files
- `GoldManager_FastAdmin_API/env_config.txt`: Backend environment variables
- `GoldManager_FastAdmin_API/pyproject.toml`: Ruff configuration and Python project settings
- `GoldManager_FastAdmin_API/Makefile`: Development automation commands
- `gold_manager_flutter/lib/core/config/app_config.dart`: Frontend API endpoints
- `gold_manager_flutter/analysis_options.yaml`: Dart/Flutter linting rules

### Authentication & Permissions
- Backend permission check: `require_permission("jewelry.view")`
- Frontend permission widget: `PermissionWidget(permission: "jewelry.edit")`

### Service Patterns
- Backend services receive database session via dependency injection
- Frontend services use `Get.find<ApiService>()` for API calls
- Both follow async/await patterns for asynchronous operations

### Testing Approach
- Backend: pytest with async support (`pytest tests/ -v`)
  - Uses httpx for async HTTP testing
  - Test files in `tests/` directory
- Frontend: flutter_test with widget and unit tests (`flutter test`)
- Mock services for isolated testing
- Use `make test` for automated backend testing

## Database Compatibility Notes

This system is designed to work with existing FastAdmin databases:
- All table names use `fa_` prefix
- Timestamps stored as integers (Unix timestamps)
- Password hashing uses FastAdmin's salt + md5 method
- Maintains compatibility with PHP-generated data

## Debugging Tips

1. **Backend API Issues**:
   - Check `logs/api.log` for detailed error messages
   - Verify database connection in `env_config.txt`
   - Use Swagger UI at `/docs` for testing endpoints
   - Use `make check` for comprehensive code quality analysis
   - Check reports in `reports/` directory for detailed analysis

2. **Frontend State Issues**:
   - Use `Get.log = true` for GetX debugging
   - Check browser/app console for network errors
   - Verify API base URL in `app_config.dart`
   - Use `flutter analyze` for static analysis warnings

3. **Authentication Problems**:
   - Ensure tokens are being sent (check network tab)
   - Verify user permissions in database
   - Check token expiration settings

4. **Development Environment**:
   - Use `make help` to see all available commands
   - Run `scripts/install_dependencies.sh` for complete environment setup
   - Use `make clean` to clear temporary files and caches