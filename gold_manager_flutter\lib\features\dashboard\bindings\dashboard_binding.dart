import 'package:get/get.dart';

import '../controllers/dashboard_controller.dart';
import '../controllers/dashboard_data_controller.dart';
import '../../jewelry/bindings/jewelry_binding.dart';
import '../../stock/bindings/stock_in_binding.dart';
import '../../stock/bindings/stock_out_binding.dart';
import '../../stock/bindings/stock_query_binding.dart';
import '../../stock/controllers/stock_tab_controller.dart';
import '../../sales/bindings/sales_binding.dart';
import '../../recycling/controllers/recycling_tab_controller.dart';

/// 仪表盘模块依赖绑定
class DashboardBinding extends Bindings {
  @override
  void dependencies() {
    // 仪表板控制器
    Get.lazyPut<DashboardController>(() => DashboardController());

    // 仪表板数据控制器 - 立即创建并持久化
    Get.put<DashboardDataController>(
      DashboardDataController(),
      permanent: true, // 永久保持，不会被销毁
    );

    // 全局注册StockTabController，确保所有页面都能访问
    Get.lazyPut<StockTabController>(
      () => StockTabController(),
      fenix: true, // 允许重新创建
    );

    // 全局注册RecyclingTabController，确保回收单页面都能访问
    Get.lazyPut<RecyclingTabController>(
      () => RecyclingTabController(),
      fenix: true, // 允许重新创建
    );

    // 首饰管理控制器（因为首饰管理现在是仪表板的一部分）
    JewelryBinding().dependencies();

    // 库存管理控制器（因为库存管理现在是仪表板的一部分）
    StockInBinding().dependencies();
    StockOutBinding().dependencies();
    StockQueryBinding().dependencies();

    // 销售管理控制器（因为销售管理现在是仪表板的一部分）
    SalesBinding().dependencies();
  }
}
