import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';

import '../../../core/utils/logger.dart';
import '../services/dashboard_service.dart';
import '../models/dashboard_data.dart';
import '../widgets/sales_overview_widget.dart';
import '../widgets/inventory_overview_widget.dart';
import '../widgets/recycling_overview_widget.dart';
import '../widgets/financial_overview_widget.dart';

/// 仪表盘数据控制器
/// 专门负责仪表盘数据的获取、管理和刷新
class DashboardDataController extends GetxController {
  /// 仪表盘服务
  final DashboardService _dashboardService = DashboardService();
  
  /// 数据刷新定时器
  Timer? _refreshTimer;

  /// 数据缓存时间戳
  DateTime? _lastLoadTime;

  /// 缓存有效期（5分钟）
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  // 数据状态
  final RxBool isLoading = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;

  /// 是否有缓存数据
  bool get hasCachedData => _lastLoadTime != null &&
    DateTime.now().difference(_lastLoadTime!) < _cacheValidDuration;

  // 数据缓存时间戳
  DateTime? _lastOverviewUpdate;
  DateTime? _lastSalesUpdate;
  DateTime? _lastInventoryUpdate;
  DateTime? _lastRecyclingUpdate;
  DateTime? _lastFinancialUpdate;

  // 缓存有效期（分钟）
  static const int _cacheValidityMinutes = 5;

  // 仪表盘数据
  final Rx<DashboardOverviewData> overviewData = DashboardOverviewData.empty.obs;
  final Rx<SalesOverviewData> salesData = SalesOverviewData.empty.obs;
  final Rx<InventoryOverviewData> inventoryData = InventoryOverviewData.empty.obs;
  final Rx<RecyclingOverviewData> recyclingData = RecyclingOverviewData.empty.obs;
  final Rx<FinancialOverviewData> financialData = FinancialOverviewData.empty.obs;

  @override
  void onInit() {
    super.onInit();
    LoggerService.d('🎯 DashboardDataController 初始化');

    // 智能加载：如果有有效缓存，立即设置为非加载状态并显示缓存数据
    if (hasCachedData) {
      LoggerService.d('📦 使用缓存数据，立即显示，后台刷新');
      // 立即设置为非加载状态
      isLoading.value = false;
      hasError.value = false;
      // 后台静默刷新
      Future.delayed(const Duration(milliseconds: 500), () {
        _refreshDataSilently();
      });
    } else {
      LoggerService.d('🔄 首次加载数据');
      // 首次加载数据
      loadDashboardData();
    }

    // 设置自动刷新定时器 (每2分钟)
    _startAutoRefresh();
  }

  @override
  void onClose() {
    _refreshTimer?.cancel();
    super.onClose();
  }

  /// 加载仪表盘数据 - 优化版本：分层加载
  Future<void> loadDashboardData() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      LoggerService.d('🔄 开始分层加载仪表盘数据');

      // 第一层：核心概览数据（最重要，优先加载）
      await _loadOverviewData();

      // 第二层：业务数据（并行加载，但不阻塞UI）
      _loadBusinessDataInBackground();

      // 更新缓存时间戳
      _lastLoadTime = DateTime.now();

      LoggerService.d('✅ 核心数据加载完成，业务数据后台加载中');

    } catch (e) {
      LoggerService.e('❌ 仪表盘数据加载失败', e);
      hasError.value = true;
      errorMessage.value = e.toString();

      // 显示错误提示
      Get.snackbar(
        '数据加载失败',
        '无法获取仪表盘数据，请检查网络连接',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 后台加载业务数据
  Future<void> _loadBusinessDataInBackground() async {
    try {
      // 并行加载业务数据，但不影响主界面显示
      await Future.wait([
        _loadSalesData(),
        _loadInventoryData(),
        _loadRecyclingData(),
        _loadFinancialData(),
      ]);

      LoggerService.d('✅ 所有业务数据加载完成');
    } catch (e) {
      LoggerService.e('❌ 业务数据加载失败', e);
      // 业务数据加载失败不影响主界面，只记录日志
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    await loadDashboardData();
  }

  /// 快速恢复数据（从其他页面返回时使用）
  Future<void> quickRestore() async {
    LoggerService.d('🚀 快速恢复仪表盘数据');

    // 如果有有效缓存，立即设置为非加载状态并返回
    if (hasCachedData) {
      LoggerService.d('📦 使用有效缓存，立即显示数据');
      isLoading.value = false;
      hasError.value = false;
      return;
    }

    // 如果缓存过期，进行快速刷新
    LoggerService.d('🔄 缓存过期，执行快速刷新');
    await _refreshDataSilently();
  }

  /// 启动智能自动刷新
  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      // 智能刷新：只在非加载状态且页面可见时刷新
      if (!isLoading.value && Get.currentRoute.contains('dashboard')) {
        _refreshDataSilently();
      }
    });
  }

  /// 静默刷新数据（不显示loading状态）
  Future<void> _refreshDataSilently() async {
    try {
      LoggerService.d('🔄 静默刷新仪表盘数据');

      // 只刷新核心数据，避免频繁的完整刷新
      await _loadOverviewData();

      // 后台更新业务数据
      _loadBusinessDataInBackground();

    } catch (e) {
      LoggerService.e('❌ 静默刷新失败', e);
      // 静默刷新失败不显示错误提示
    }
  }

  /// 停止自动刷新
  void stopAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// 重新启动自动刷新
  void restartAutoRefresh() {
    stopAutoRefresh();
    _startAutoRefresh();
  }

  /// 加载概览数据（带缓存）
  Future<void> _loadOverviewData() async {
    // 检查缓存是否有效
    if (_isCacheValid(_lastOverviewUpdate)) {
      LoggerService.d('📦 使用缓存的概览数据');
      return;
    }

    try {
      final data = await _dashboardService.getOverviewData();
      overviewData.value = data;
      _lastOverviewUpdate = DateTime.now();
      LoggerService.d('✅ 概览数据加载并缓存成功');
    } catch (e) {
      LoggerService.e('概览数据加载失败', e);
      // 使用示例数据作为fallback
      overviewData.value = const DashboardOverviewData(
        totalProducts: 2580,
        totalStores: 3,
        totalMembers: 1250,
        todaySales: 25680,
        monthSales: 456780,
        inventoryValue: 1250000,
        pendingOrders: 8,
      );
      _lastOverviewUpdate = DateTime.now();
    }
  }

  /// 检查缓存是否有效
  bool _isCacheValid(DateTime? lastUpdate) {
    if (lastUpdate == null) return false;
    final now = DateTime.now();
    final difference = now.difference(lastUpdate).inMinutes;
    return difference < _cacheValidityMinutes;
  }

  /// 加载销售数据（带缓存）
  Future<void> _loadSalesData() async {
    // 检查缓存是否有效
    if (_isCacheValid(_lastSalesUpdate)) {
      LoggerService.d('📦 使用缓存的销售数据');
      return;
    }

    try {
      final stats = await _dashboardService.getSalesStatistics();
      final trend = await _dashboardService.getSalesTrend();

      // 转换为销售概览数据
      salesData.value = SalesOverviewData(
        todaySales: stats.totalAmount * 0.1, // 假设今日占总额的10%
        todayTrend: 12.5,
        monthSales: stats.totalAmount,
        monthTrend: 8.3,
        orderCount: stats.totalOrders,
        orderTrend: 15.2,
        avgOrderValue: stats.avgOrderValue,
        avgOrderTrend: -2.1,
        salesTrendData: trend.trendData,
        topProductsData: const [8500, 7200, 6800, 5900, 5200],
      );
      _lastSalesUpdate = DateTime.now();
      LoggerService.d('✅ 销售数据加载并缓存成功');
    } catch (e) {
      LoggerService.e('销售数据加载失败', e);
      salesData.value = SalesOverviewData.sample;
      _lastSalesUpdate = DateTime.now();
    }
  }

  /// 加载库存数据（带缓存）
  Future<void> _loadInventoryData() async {
    // 检查缓存是否有效
    if (_isCacheValid(_lastInventoryUpdate)) {
      LoggerService.d('📦 使用缓存的库存数据');
      return;
    }

    try {
      final stats = await _dashboardService.getInventoryStatistics();

      // 转换为库存概览数据
      inventoryData.value = InventoryOverviewData(
        totalValue: stats.totalValue,
        valueTrend: 5.2,
        totalItems: stats.totalItems,
        itemsTrend: 3.1,
        lowStockCount: stats.lowStockCount,
        turnoverRate: stats.turnoverRate,
        turnoverTrend: 8.5,
        categoryDistribution: stats.categoryDistribution,
        inboundCount: 156,
        outboundCount: 142,
        totalGoldWeight: 1250.5,
        totalSilverWeight: 850.2,
        pendingCheckCount: 8,
        differenceCount: 3,
      );
      _lastInventoryUpdate = DateTime.now();
      LoggerService.d('✅ 库存数据加载并缓存成功');
    } catch (e) {
      LoggerService.e('库存数据加载失败', e);
      inventoryData.value = InventoryOverviewData.sample;
      _lastInventoryUpdate = DateTime.now();
    }
  }

  /// 加载回收数据（带缓存）
  Future<void> _loadRecyclingData() async {
    // 检查缓存是否有效
    if (_isCacheValid(_lastRecyclingUpdate)) {
      LoggerService.d('📦 使用缓存的回收数据');
      return;
    }

    try {
      // 这里应该调用回收相关的API
      // 暂时使用示例数据
      recyclingData.value = RecyclingOverviewData.sample;
      _lastRecyclingUpdate = DateTime.now();
      LoggerService.d('✅ 回收数据加载并缓存成功');
    } catch (e) {
      LoggerService.e('回收数据加载失败', e);
      recyclingData.value = RecyclingOverviewData.sample;
      _lastRecyclingUpdate = DateTime.now();
    }
  }

  /// 加载财务数据（带缓存）
  Future<void> _loadFinancialData() async {
    // 检查缓存是否有效
    if (_isCacheValid(_lastFinancialUpdate)) {
      LoggerService.d('📦 使用缓存的财务数据');
      return;
    }

    try {
      final stats = await _dashboardService.getFinancialStatistics();

      // 转换为财务概览数据
      financialData.value = FinancialOverviewData(
        totalRevenue: stats.totalRevenue,
        revenueTrend: 8.5,
        totalCost: stats.totalCost,
        costTrend: 5.2,
        netProfit: stats.netProfit,
        profitTrend: 15.8,
        profitMargin: stats.profitMargin,
        marginTrend: 2.3,
        profitTrendData: stats.profitTrend,
        salesRevenue: 65.5,
        recyclingRevenue: 34.5,
        operatingCost: 45.2,
        materialCost: 54.8,
        accountsReceivable: 125000,
        accountsPayable: 85000,
        cashFlow: 180000,
        totalAssets: 2500000,
        totalLiabilities: 650000,
        netAssets: 1850000,
      );
      _lastFinancialUpdate = DateTime.now();
      LoggerService.d('✅ 财务数据加载并缓存成功');
    } catch (e) {
      LoggerService.e('财务数据加载失败', e);
      financialData.value = FinancialOverviewData.sample;
      _lastFinancialUpdate = DateTime.now();
    }
  }

  /// 获取指定时间范围的销售数据
  Future<void> loadSalesDataByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final stats = await _dashboardService.getSalesStatistics(
        startDate: startDate.toIso8601String().split('T')[0],
        endDate: endDate.toIso8601String().split('T')[0],
      );
      
      // 更新销售数据
      salesData.value = salesData.value.copyWith(
        monthSales: stats.totalAmount,
        orderCount: stats.totalOrders,
        avgOrderValue: stats.avgOrderValue,
      );
    } catch (e) {
      LoggerService.e('指定时间范围销售数据加载失败', e);
    }
  }

  /// 获取指定门店的数据
  Future<void> loadDataByStore(int storeId) async {
    try {
      final salesStats = await _dashboardService.getSalesStatistics(storeId: storeId);
      final inventoryStats = await _dashboardService.getInventoryStatistics(storeId: storeId);
      
      // 更新数据
      salesData.value = salesData.value.copyWith(
        monthSales: salesStats.totalAmount,
        orderCount: salesStats.totalOrders,
        avgOrderValue: salesStats.avgOrderValue,
      );
      
      inventoryData.value = inventoryData.value.copyWith(
        totalValue: inventoryStats.totalValue,
        totalItems: inventoryStats.totalItems,
        lowStockCount: inventoryStats.lowStockCount,
      );
    } catch (e) {
      LoggerService.e('指定门店数据加载失败', e);
    }
  }
}

/// 扩展方法，为数据模型添加copyWith功能
extension SalesOverviewDataExtension on SalesOverviewData {
  SalesOverviewData copyWith({
    double? monthSales,
    int? orderCount,
    double? avgOrderValue,
  }) {
    return SalesOverviewData(
      todaySales: todaySales,
      todayTrend: todayTrend,
      monthSales: monthSales ?? this.monthSales,
      monthTrend: monthTrend,
      orderCount: orderCount ?? this.orderCount,
      orderTrend: orderTrend,
      avgOrderValue: avgOrderValue ?? this.avgOrderValue,
      avgOrderTrend: avgOrderTrend,
      salesTrendData: salesTrendData,
      topProductsData: topProductsData,
    );
  }
}

extension InventoryOverviewDataExtension on InventoryOverviewData {
  InventoryOverviewData copyWith({
    double? totalValue,
    int? totalItems,
    int? lowStockCount,
  }) {
    return InventoryOverviewData(
      totalValue: totalValue ?? this.totalValue,
      valueTrend: valueTrend,
      totalItems: totalItems ?? this.totalItems,
      itemsTrend: itemsTrend,
      lowStockCount: lowStockCount ?? this.lowStockCount,
      turnoverRate: turnoverRate,
      turnoverTrend: turnoverTrend,
      categoryDistribution: categoryDistribution,
      inboundCount: inboundCount,
      outboundCount: outboundCount,
      totalGoldWeight: totalGoldWeight,
      totalSilverWeight: totalSilverWeight,
      pendingCheckCount: pendingCheckCount,
      differenceCount: differenceCount,
    );
  }
}
