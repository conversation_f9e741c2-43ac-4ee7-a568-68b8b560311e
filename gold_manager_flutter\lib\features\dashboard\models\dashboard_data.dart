/// 仪表盘数据模型
/// 定义所有仪表盘相关的数据结构
library dashboard_data;
import '../../../core/utils/number_utils.dart';


/// 仪表盘概览数据
class DashboardOverviewData {
  final int totalProducts;
  final int totalStores;
  final int totalMembers;
  final double todaySales;
  final double monthSales;
  final double inventoryValue;
  final int pendingOrders;

  const DashboardOverviewData({
    required this.totalProducts,
    required this.totalStores,
    required this.totalMembers,
    required this.todaySales,
    required this.monthSales,
    required this.inventoryValue,
    required this.pendingOrders,
  });

  factory DashboardOverviewData.fromJson(Map<String, dynamic> json) {
    // 兼容后端多种字段命名
    final totalProducts = json['total_products'] ?? json['total_jewelry'] ?? 0;
    final todaySalesRaw = json['today_sales'] ?? json['today_sales_amount'] ?? 0;
    final monthSalesRaw = json['month_sales'] ?? json['month_sales_amount'] ?? 0;
    final inventoryValueRaw = json['inventory_value'] ?? json['total_inventory_value'] ?? 0;

    // 待处理订单：优先使用统一字段，否则汇总各类待处理数
    final pendingOrders = json['pending_orders'] ?? (
      (json['pending_stock_in'] ?? 0) +
      (json['pending_stock_out'] ?? 0) +
      (json['pending_returns'] ?? 0) +
      (json['pending_transfers'] ?? 0)
    );

    return DashboardOverviewData(
      totalProducts: totalProducts is int ? totalProducts : int.tryParse(totalProducts.toString()) ?? 0,
      totalStores: json['total_stores'] ?? 0,
      totalMembers: json['total_members'] ?? 0,
      todaySales: NumberUtils.toDouble(todaySalesRaw),
      monthSales: NumberUtils.toDouble(monthSalesRaw),
      inventoryValue: NumberUtils.toDouble(inventoryValueRaw),
      pendingOrders: pendingOrders is int ? pendingOrders : int.tryParse(pendingOrders.toString()) ?? 0,
    );
  }

  static const DashboardOverviewData empty = DashboardOverviewData(
    totalProducts: 0,
    totalStores: 0,
    totalMembers: 0,
    todaySales: 0,
    monthSales: 0,
    inventoryValue: 0,
    pendingOrders: 0,
  );
}

/// 销售统计数据
class SalesStatisticsData {
  final double totalAmount;
  final int totalOrders;
  final double avgOrderValue;
  final double totalProfit;
  final double profitMargin;
  final List<double> trendData;

  const SalesStatisticsData({
    required this.totalAmount,
    required this.totalOrders,
    required this.avgOrderValue,
    required this.totalProfit,
    required this.profitMargin,
    required this.trendData,
  });

  factory SalesStatisticsData.fromJson(Map<String, dynamic> json) {
    final avg = json['avg_order_value'] ?? json['average_order_value'] ?? 0;
    return SalesStatisticsData(
      totalAmount: NumberUtils.toDouble(json['total_amount']),
      totalOrders: json['total_orders'] ?? 0,
      avgOrderValue: NumberUtils.toDouble(avg),
      totalProfit: NumberUtils.toDouble(json['total_profit']),
      profitMargin: NumberUtils.toDouble(json['profit_margin']),
      trendData: (json['trend_data'] as List?)?.map((e) => (e as num).toDouble()).toList() ?? <double>[],
    );
  }

  static const SalesStatisticsData empty = SalesStatisticsData(
    totalAmount: 0,
    totalOrders: 0,
    avgOrderValue: 0,
    totalProfit: 0,
    profitMargin: 0,
    trendData: [],
  );
}

/// 库存统计数据
class InventoryStatisticsData {
  final double totalValue;
  final int totalItems;
  final int lowStockCount;
  final double turnoverRate;
  final List<double> categoryDistribution;

  const InventoryStatisticsData({
    required this.totalValue,
    required this.totalItems,
    required this.lowStockCount,
    required this.turnoverRate,
    required this.categoryDistribution,
  });

  factory InventoryStatisticsData.fromJson(Map<String, dynamic> json) {
    final lowStock = json['low_stock_count'] ?? json['low_stock_items'] ?? 0;
    final categoryDist = json['category_distribution'];
    // 兼容后端两种结构: 数值列表或对象列表
    List<double> dist;
    if (categoryDist is List) {
      if (categoryDist.isNotEmpty && categoryDist.first is Map) {
        dist = categoryDist
            .map((e) => (e['value'] ?? e['count'] ?? 0))
            .map<double>((e) => (e as num).toDouble())
            .toList();
      } else {
        dist = categoryDist.map<double>((e) => (e as num).toDouble()).toList();
      }
    } else {
      dist = <double>[];
    }
    return InventoryStatisticsData(
      totalValue: NumberUtils.toDouble(json['total_value']),
      totalItems: json['total_items'] ?? 0,
      lowStockCount: lowStock is int ? lowStock : int.tryParse(lowStock.toString()) ?? 0,
      turnoverRate: NumberUtils.toDouble(json['turnover_rate'] ?? json['monthly_turnover_rate']),
      categoryDistribution: dist,
    );
  }

  static const InventoryStatisticsData empty = InventoryStatisticsData(
    totalValue: 0,
    totalItems: 0,
    lowStockCount: 0,
    turnoverRate: 0,
    categoryDistribution: [],
  );
}

/// 会员统计数据
class MemberStatisticsData {
  final int totalMembers;
  final int newMembers;
  final int activeMembers;
  final List<int> memberGrowth;

  const MemberStatisticsData({
    required this.totalMembers,
    required this.newMembers,
    required this.activeMembers,
    required this.memberGrowth,
  });

  factory MemberStatisticsData.fromJson(Map<String, dynamic> json) {
    return MemberStatisticsData(
      totalMembers: json['total_members'] ?? 0,
      newMembers: json['new_members'] ?? 0,
      activeMembers: json['active_members'] ?? 0,
      memberGrowth: (json['member_growth'] as List?)?.map((e) => e as int).toList() ?? [],
    );
  }

  static const MemberStatisticsData empty = MemberStatisticsData(
    totalMembers: 0,
    newMembers: 0,
    activeMembers: 0,
    memberGrowth: [],
  );
}

/// 财务统计数据
class FinancialStatisticsData {
  final double totalRevenue;
  final double totalCost;
  final double netProfit;
  final double profitMargin;
  final List<double> profitTrend;

  const FinancialStatisticsData({
    required this.totalRevenue,
    required this.totalCost,
    required this.netProfit,
    required this.profitMargin,
    required this.profitTrend,
  });

  factory FinancialStatisticsData.fromJson(Map<String, dynamic> json) {
    final profitTrend = json['profit_trend'] ?? json['monthly_trend'] ?? [];
    // 兼容数值数组或对象数组({value: ...})
    List<double> trend;
    if (profitTrend is List) {
      if (profitTrend.isNotEmpty && profitTrend.first is Map) {
        trend = profitTrend
            .map((e) => (e['value'] ?? 0))
            .map<double>((e) => (e as num).toDouble())
            .toList();
      } else {
        trend = profitTrend.map<double>((e) => (e as num).toDouble()).toList();
      }
    } else {
      trend = <double>[];
    }
    return FinancialStatisticsData(
      totalRevenue: NumberUtils.toDouble(json['total_revenue']),
      totalCost: NumberUtils.toDouble(json['total_cost']),
      netProfit: NumberUtils.toDouble(json['net_profit'] ?? (NumberUtils.toDouble(json['total_revenue']) - NumberUtils.toDouble(json['total_cost']))),
      profitMargin: NumberUtils.toDouble(json['profit_margin']),
      profitTrend: trend,
    );
  }

  static const FinancialStatisticsData empty = FinancialStatisticsData(
    totalRevenue: 0,
    totalCost: 0,
    netProfit: 0,
    profitMargin: 0,
    profitTrend: [],
  );
}

/// 销售趋势数据
class SalesTrendData {
  final List<double> trendData;
  final List<String> labels;

  const SalesTrendData({
    required this.trendData,
    required this.labels,
  });

  factory SalesTrendData.fromJson(Map<String, dynamic> json) {
    return SalesTrendData(
      trendData: (json['trend_data'] as List?)?.map((e) => (e as num).toDouble()).toList() ?? <double>[],
      labels: (json['labels'] as List?)?.map((e) => e.toString()).toList() ?? [],
    );
  }

  static const SalesTrendData empty = SalesTrendData(
    trendData: [],
    labels: [],
  );
}

/// 门店对比数据
class StoreComparisonData {
  final List<String> storeNames;
  final List<double> salesData;

  const StoreComparisonData({
    required this.storeNames,
    required this.salesData,
  });

  factory StoreComparisonData.fromJson(Map<String, dynamic> json) {
    return StoreComparisonData(
      storeNames: (json['store_names'] as List?)?.map((e) => e.toString()).toList() ?? [],
      salesData: (json['sales_data'] as List?)?.map((e) => (e as num).toDouble()).toList() ?? <double>[],
    );
  }

  static const StoreComparisonData empty = StoreComparisonData(
    storeNames: [],
    salesData: [],
  );
}

/// 商品排行数据
class CategoryRankingData {
  final List<String> categoryNames;
  final List<double> salesData;

  const CategoryRankingData({
    required this.categoryNames,
    required this.salesData,
  });

  factory CategoryRankingData.fromJson(Map<String, dynamic> json) {
    return CategoryRankingData(
      categoryNames: (json['category_names'] as List?)?.map((e) => e.toString()).toList() ?? [],
      salesData: (json['sales_data'] as List?)?.map((e) => (e as num).toDouble()).toList() ?? <double>[],
    );
  }

  static const CategoryRankingData empty = CategoryRankingData(
    categoryNames: [],
    salesData: [],
  );
}

/// 低库存预警数据
class LowStockAlertsData {
  final List<String> productNames;
  final List<int> stockCounts;

  const LowStockAlertsData({
    required this.productNames,
    required this.stockCounts,
  });

  factory LowStockAlertsData.fromJson(Map<String, dynamic> json) {
    return LowStockAlertsData(
      productNames: (json['product_names'] as List?)?.map((e) => e.toString()).toList() ?? [],
      stockCounts: (json['stock_counts'] as List?)?.map((e) => e as int).toList() ?? [],
    );
  }

  static const LowStockAlertsData empty = LowStockAlertsData(
    productNames: [],
    stockCounts: [],
  );
}

/// 会员增长数据
class MemberGrowthData {
  final List<int> growthData;
  final List<String> labels;

  const MemberGrowthData({
    required this.growthData,
    required this.labels,
  });

  factory MemberGrowthData.fromJson(Map<String, dynamic> json) {
    return MemberGrowthData(
      growthData: (json['growth_data'] as List?)?.map((e) => e as int).toList() ?? [],
      labels: (json['labels'] as List?)?.map((e) => e.toString()).toList() ?? [],
    );
  }

  static const MemberGrowthData empty = MemberGrowthData(
    growthData: [],
    labels: [],
  );
}
