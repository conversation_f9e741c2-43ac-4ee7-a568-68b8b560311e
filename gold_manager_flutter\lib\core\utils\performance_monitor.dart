import 'dart:async';
import 'logger_service.dart';

/// 性能监控工具
/// 用于监控和分析应用性能瓶颈
class PerformanceMonitor {
  static final Map<String, DateTime> _startTimes = {};
  static final Map<String, List<int>> _durations = {};
  
  /// 开始监控某个操作
  static void startTimer(String operation) {
    _startTimes[operation] = DateTime.now();
    LoggerService.d('⏱️ 开始监控: $operation');
  }
  
  /// 结束监控并记录耗时
  static void endTimer(String operation) {
    final startTime = _startTimes[operation];
    if (startTime == null) {
      LoggerService.w('⚠️ 未找到操作的开始时间: $operation');
      return;
    }
    
    final duration = DateTime.now().difference(startTime).inMilliseconds;
    _durations.putIfAbsent(operation, () => []).add(duration);
    
    LoggerService.d('⏱️ 操作完成: $operation, 耗时: ${duration}ms');
    
    // 如果耗时超过阈值，发出警告
    if (duration > 2000) { // 2秒
      LoggerService.w('🐌 性能警告: $operation 耗时过长 (${duration}ms)');
    }
    
    _startTimes.remove(operation);
  }
  
  /// 监控异步操作
  static Future<T> monitorAsync<T>(String operation, Future<T> Function() task) async {
    startTimer(operation);
    try {
      final result = await task();
      endTimer(operation);
      return result;
    } catch (e) {
      endTimer(operation);
      LoggerService.e('❌ 监控的异步操作失败: $operation', e);
      rethrow;
    }
  }
  
  /// 获取操作的平均耗时
  static double getAverageTime(String operation) {
    final durations = _durations[operation];
    if (durations == null || durations.isEmpty) return 0.0;
    
    final sum = durations.reduce((a, b) => a + b);
    return sum / durations.length;
  }
  
  /// 获取性能报告
  static String getPerformanceReport() {
    final buffer = StringBuffer();
    buffer.writeln('📊 性能监控报告');
    buffer.writeln('=' * 50);
    
    for (final entry in _durations.entries) {
      final operation = entry.key;
      final durations = entry.value;
      final avgTime = getAverageTime(operation);
      final maxTime = durations.reduce((a, b) => a > b ? a : b);
      final minTime = durations.reduce((a, b) => a < b ? a : b);
      
      buffer.writeln('操作: $operation');
      buffer.writeln('  执行次数: ${durations.length}');
      buffer.writeln('  平均耗时: ${avgTime.toStringAsFixed(1)}ms');
      buffer.writeln('  最大耗时: ${maxTime}ms');
      buffer.writeln('  最小耗时: ${minTime}ms');
      buffer.writeln();
    }
    
    return buffer.toString();
  }
  
  /// 清除监控数据
  static void clear() {
    _startTimes.clear();
    _durations.clear();
    LoggerService.d('🧹 性能监控数据已清除');
  }
  
  /// 打印性能报告
  static void printReport() {
    LoggerService.d(getPerformanceReport());
  }
}

/// 性能监控装饰器
class PerformanceDecorator {
  /// 装饰异步方法
  static Future<T> decorateAsync<T>(
    String operationName,
    Future<T> Function() method,
  ) async {
    return PerformanceMonitor.monitorAsync(operationName, method);
  }
  
  /// 装饰同步方法
  static T decorateSync<T>(
    String operationName,
    T Function() method,
  ) {
    PerformanceMonitor.startTimer(operationName);
    try {
      final result = method();
      PerformanceMonitor.endTimer(operationName);
      return result;
    } catch (e) {
      PerformanceMonitor.endTimer(operationName);
      rethrow;
    }
  }
}
