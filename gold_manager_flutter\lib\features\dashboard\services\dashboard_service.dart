import 'package:get/get.dart';
import '../../../core/services/api_service.dart';
import '../../../core/utils/logger.dart';
import '../models/dashboard_data.dart';

/// 仪表盘数据服务
/// 负责从后端API获取仪表盘相关数据
class DashboardService {
  ApiService get _apiService => Get.find<ApiService>();

  /// 兼容多种后端返回格式，统一取出数据部分
  Map<String, dynamic> _unwrapData(dynamic body) {
    if (body is Map<String, dynamic>) {
      // StandardResponse 格式: { success, message, data, code }
      if (body.containsKey('success') && body.containsKey('data')) {
        if (body['success'] == true) {
          return (body['data'] as Map).cast<String, dynamic>();
        }
        throw Exception(body['message'] ?? '请求失败');
      }
      // FastAdmin 风格: { code, msg, data }
      if (body.containsKey('code')) {
        if (body['code'] == 0) {
          return (body['data'] as Map).cast<String, dynamic>();
        }
        throw Exception(body['msg'] ?? '请求失败');
      }
      // 直接返回数据对象
      return body.cast<String, dynamic>();
    }
    throw Exception('响应格式不正确');
  }

  /// 获取仪表盘概览数据
  Future<DashboardOverviewData> getOverviewData() async {
    try {
      LoggerService.d('🔄 获取仪表盘概览数据');
      final response = await _apiService.get('/api/v1/dashboard/overview');
      if (response.statusCode == 200) {
        final data = _unwrapData(response.data);
        LoggerService.d('✅ 仪表盘概览数据获取成功');
        return DashboardOverviewData.fromJson(data);
      } else {
        throw Exception('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取仪表盘概览数据失败', e);
      rethrow;
    }
  }

  /// 获取销售统计数据
  Future<SalesStatisticsData> getSalesStatistics({
    String? startDate,
    String? endDate,
    int? storeId,
  }) async {
    try {
      LoggerService.d('🔄 获取销售统计数据');
      final params = <String, dynamic>{};
      if (startDate != null) params['start_date'] = startDate;
      if (endDate != null) params['end_date'] = endDate;
      if (storeId != null) params['store_id'] = storeId;
      final response = await _apiService.get(
        '/api/v1/dashboard/sales-statistics',
        queryParameters: params,
      );
      if (response.statusCode == 200) {
        final data = _unwrapData(response.data);
        LoggerService.d('✅ 销售统计数据获取成功');
        return SalesStatisticsData.fromJson(data);
      } else {
        throw Exception('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取销售统计数据失败', e);
      rethrow;
    }
  }

  /// 获取库存统计数据
  Future<InventoryStatisticsData> getInventoryStatistics({
    int? storeId,
  }) async {
    try {
      LoggerService.d('🔄 获取库存统计数据');
      final params = <String, dynamic>{};
      if (storeId != null) params['store_id'] = storeId;
      final response = await _apiService.get(
        '/api/v1/dashboard/inventory-statistics',
        queryParameters: params,
      );
      if (response.statusCode == 200) {
        final data = _unwrapData(response.data);
        LoggerService.d('✅ 库存统计数据获取成功');
        return InventoryStatisticsData.fromJson(data);
      } else {
        throw Exception('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取库存统计数据失败', e);
      rethrow;
    }
  }

  /// 获取会员统计数据
  Future<MemberStatisticsData> getMemberStatistics({
    String? startDate,
    String? endDate,
  }) async {
    try {
      LoggerService.d('🔄 获取会员统计数据');
      final params = <String, dynamic>{};
      if (startDate != null) params['start_date'] = startDate;
      if (endDate != null) params['end_date'] = endDate;
      final response = await _apiService.get(
        '/api/v1/dashboard/member-statistics',
        queryParameters: params,
      );
      if (response.statusCode == 200) {
        final data = _unwrapData(response.data);
        LoggerService.d('✅ 会员统计数据获取成功');
        return MemberStatisticsData.fromJson(data);
      } else {
        throw Exception('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取会员统计数据失败', e);
      rethrow;
    }
  }

  /// 获取财务统计数据
  Future<FinancialStatisticsData> getFinancialStatistics({
    String? startDate,
    String? endDate,
    int? storeId,
  }) async {
    try {
      LoggerService.d('🔄 获取财务统计数据');
      final params = <String, dynamic>{};
      if (startDate != null) params['start_date'] = startDate;
      if (endDate != null) params['end_date'] = endDate;
      if (storeId != null) params['store_id'] = storeId;
      final response = await _apiService.get(
        '/api/v1/dashboard/financial-statistics',
        queryParameters: params,
      );
      if (response.statusCode == 200) {
        final data = _unwrapData(response.data);
        LoggerService.d('✅ 财务统计数据获取成功');
        return FinancialStatisticsData.fromJson(data);
      } else {
        throw Exception('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取财务统计数据失败', e);
      rethrow;
    }
  }

  /// 获取销售趋势数据
  Future<SalesTrendData> getSalesTrend({
    String period = 'month',
    int days = 30,
    int? storeId,
  }) async {
    try {
      LoggerService.d('🔄 获取销售趋势数据');
      final params = <String, dynamic>{
        'period': period,
        'days': days,
      };
      if (storeId != null) params['store_id'] = storeId;
      final response = await _apiService.get(
        '/api/v1/dashboard/sales-trend',
        queryParameters: params,
      );
      if (response.statusCode == 200) {
        final data = _unwrapData(response.data);
        LoggerService.d('✅ 销售趋势数据获取成功');
        return SalesTrendData.fromJson(data);
      } else {
        throw Exception('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取销售趋势数据失败', e);
      rethrow;
    }
  }

  /// 获取门店对比数据
  Future<StoreComparisonData> getStoreComparison({
    String? startDate,
    String? endDate,
  }) async {
    try {
      LoggerService.d('🔄 获取门店对比数据');
      final params = <String, dynamic>{};
      if (startDate != null) params['start_date'] = startDate;
      if (endDate != null) params['end_date'] = endDate;
      final response = await _apiService.get(
        '/api/v1/dashboard/store-comparison',
        queryParameters: params,
      );
      if (response.statusCode == 200) {
        final data = _unwrapData(response.data);
        LoggerService.d('✅ 门店对比数据获取成功');
        return StoreComparisonData.fromJson(data);
      } else {
        throw Exception('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取门店对比数据失败', e);
      rethrow;
    }
  }

  /// 获取商品排行数据
  Future<CategoryRankingData> getCategoryRanking({
    String? startDate,
    String? endDate,
    int? storeId,
  }) async {
    try {
      LoggerService.d('🔄 获取商品排行数据');
      
      final params = <String, dynamic>{};
      if (startDate != null) params['start_date'] = startDate;
      if (endDate != null) params['end_date'] = endDate;
      if (storeId != null) params['store_id'] = storeId;
      
      final response = await _apiService.get(
        '/api/v1/dashboard/category-ranking',
        queryParameters: params,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final data = response.data['data'];
        LoggerService.d('✅ 商品排行数据获取成功');
        
        return CategoryRankingData.fromJson(data);
      } else {
        throw Exception('获取商品排行失败: ${response.data['message']}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取商品排行数据失败', e);
      rethrow;
    }
  }

  /// 获取低库存预警数据
  Future<LowStockAlertsData> getLowStockAlerts({
    int? storeId,
  }) async {
    try {
      LoggerService.d('🔄 获取低库存预警数据');
      
      final params = <String, dynamic>{};
      if (storeId != null) params['store_id'] = storeId;
      
      final response = await _apiService.get(
        '/api/v1/dashboard/low-stock-alerts',
        queryParameters: params,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final data = response.data['data'];
        LoggerService.d('✅ 低库存预警数据获取成功');
        
        return LowStockAlertsData.fromJson(data);
      } else {
        throw Exception('获取低库存预警失败: ${response.data['message']}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取低库存预警数据失败', e);
      rethrow;
    }
  }

  /// 获取会员增长数据
  Future<MemberGrowthData> getMemberGrowth({
    String? startDate,
    String? endDate,
  }) async {
    try {
      LoggerService.d('🔄 获取会员增长数据');
      
      final params = <String, dynamic>{};
      if (startDate != null) params['start_date'] = startDate;
      if (endDate != null) params['end_date'] = endDate;
      
      final response = await _apiService.get(
        '/api/v1/dashboard/member-growth',
        queryParameters: params,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final data = response.data['data'];
        LoggerService.d('✅ 会员增长数据获取成功');
        
        return MemberGrowthData.fromJson(data);
      } else {
        throw Exception('获取会员增长失败: ${response.data['message']}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取会员增长数据失败', e);
      rethrow;
    }
  }
}
